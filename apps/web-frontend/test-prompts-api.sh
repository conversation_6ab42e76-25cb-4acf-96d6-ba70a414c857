#!/bin/bash

# Test script for AI Prompts API endpoints
# This script tests the internal API proxy endpoints

BASE_URL="http://localhost:3000"
ORG_ID="test-org-123"

echo "🧪 Testing AI Prompts API Endpoints"
echo "=================================="

# Test GET endpoint (list prompts)
echo ""
echo "📋 Testing GET /api/ai/prompts (List Prompts)"
echo "URL: ${BASE_URL}/api/ai/prompts?organizationId=${ORG_ID}"
echo ""

curl -X GET \
  "${BASE_URL}/api/ai/prompts?organizationId=${ORG_ID}" \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=your-test-token" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo ""
echo "=================================="

# Test POST endpoint (create prompt)
echo ""
echo "📝 Testing POST /api/ai/prompts (Create Prompt)"
echo "URL: ${BASE_URL}/api/ai/prompts"
echo ""

curl -X POST \
  "${BASE_URL}/api/ai/prompts" \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=your-test-token" \
  -d '{
    "organizationId": "'${ORG_ID}'",
    "prompt": {
      "name": "Test Prompt",
      "description": "A test prompt for validation",
      "content": "You are a helpful AI assistant. Your name is Theo. Respond to the user'\''s query based on the provided context.",
      "isActive": true
    }
  }' \
  -w "\nStatus: %{http_code}\n" \
  -s

echo ""
echo "=================================="

# Test DELETE endpoint (delete prompt)
echo ""
echo "🗑️  Testing DELETE /api/ai/prompts (Delete Prompt)"
echo "URL: ${BASE_URL}/api/ai/prompts?organizationId=${ORG_ID}&promptId=test-prompt-id"
echo ""

curl -X DELETE \
  "${BASE_URL}/api/ai/prompts?organizationId=${ORG_ID}&promptId=test-prompt-id" \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=your-test-token" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo ""
echo "=================================="
echo "✅ API endpoint tests completed!"
echo ""
echo "Note: These tests will fail if:"
echo "- The server is not running on localhost:3000"
echo "- The external API (ANTER_API_URL) is not configured"
echo "- Authentication tokens are not valid"
echo "- The external API endpoints don't exist yet"
