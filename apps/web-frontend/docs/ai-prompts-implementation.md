# AI Prompts Management Implementation

## Overview

This document describes the complete implementation of the AI Prompts management feature for the AskInfosec organizations page. The feature allows viewing and editing LLM prompts designated to specific organizations with automatic versioning support.

## Architecture

### Data Flow

```
Frontend UI → Internal API Proxy → External API (/v1/external/prompts)
```

### Key Components

1. **Internal API Proxy** (`/api/ai/prompts/route.ts`)
2. **AI Provider Context** (`ai-provider.tsx`)
3. **Prompts Manager UI** (`prompts-manager.tsx`)
4. **Prompts Page** (`/ai/prompts/page.tsx`)

## Implementation Details

### 1. Internal API Proxy (`/api/ai/prompts/route.ts`)

**Purpose**: Serves as a secure proxy between the frontend and external AI API.

**Endpoints**:

- `GET /api/ai/prompts?organizationId={id}` - List prompts for organization
- `POST /api/ai/prompts` - Create new prompt
- `DELETE /api/ai/prompts?organizationId={id}&promptId={id}` - Delete prompt

**Authentication**: Uses internal secret mechanism with headers:

- `X-Internal-Secret`: Internal API secret
- `X-Organization-ID`: Organization identifier
- `X-Request-ID`: Unique request tracking ID
- `X-Timestamp`: Request timestamp

**Configuration**:

- `ANTER_API_URL`: Base URL for external API
- `INTERNAL_SECRET`: Secret for internal authentication
- `ANTER_API_TIMEOUT`: Request timeout (default: 30000ms)

### 2. AI Provider Context (`ai-provider.tsx`)

**Purpose**: Manages state and API interactions for AI-related features.

**Key Methods**:

- `loadPrompts()`: Fetches prompts from API on initialization
- `addPrompt(promptData)`: Creates new prompt
- `updatePrompt(id, updates)`: Creates new version of existing prompt
- `deletePrompt(id)`: Deletes prompt
- `togglePromptActive(id)`: Toggles prompt active status

**State Management**:

- Automatic data loading on mount and refresh
- Error handling with user-friendly messages
- Loading states for UI feedback

### 3. Prompts Manager UI (`prompts-manager.tsx`)

**Features**:

- Create new prompts with form validation
- Edit existing prompts (creates new versions)
- Delete prompts with confirmation
- Toggle active/inactive status
- Display versioning information
- Responsive design with loading states

**UI Components**:

- Form with name, description, content fields
- Prompt list with action buttons
- Version management notices
- Error handling with toast notifications

### 4. Prompts Page (`/ai/prompts/page.tsx`)

**Purpose**: Main page for prompt management within organization settings.

**Features**:

- Page header with title and description
- AI Provider context wrapper
- Prompts Manager component integration

## Versioning System

### How It Works

- **Updates create new versions**: When a prompt is updated, a new version is created rather than modifying the existing one
- **Automatic latest version**: New versions automatically become the latest version used by the AI system
- **Previous versions preserved**: Old versions are kept for reference and rollback capabilities

### User Experience

- Clear messaging about version creation
- "Create New Version" button text for updates
- Version management notices in the UI
- Timestamp display for last update

## API Integration

### External API Endpoints

- `GET /v1/external/prompts?organizationId={id}` - List prompts
- `POST /v1/external/prompts` - Create prompt
- `DELETE /v1/external/prompts/{promptId}` - Delete prompt

### Request/Response Format

**Create Prompt Request**:

```json
{
  "organizationId": "org-123",
  "prompt": {
    "name": "System Prompt",
    "description": "Main system prompt for AI responses",
    "content": "You are a helpful AI assistant...",
    "isActive": true
  }
}
```

**Prompt Response**:

```json
{
  "success": true,
  "prompt": {
    "id": "prompt-456",
    "name": "System Prompt",
    "description": "Main system prompt for AI responses",
    "content": "You are a helpful AI assistant...",
    "isActive": true,
    "version": 2,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Error Handling

### Frontend

- Form validation for required fields
- API error display with toast notifications
- Loading states during operations
- Graceful fallbacks for network issues

### Backend

- Comprehensive error logging
- Timeout handling
- HTTP status code mapping
- Detailed error messages

## Testing

### Manual Testing

1. Navigate to `/[organizationId]/settings/ai/prompts`
2. Create a new prompt
3. Edit an existing prompt (creates new version)
4. Toggle prompt active status
5. Delete a prompt

### API Testing

Use the provided test script:

```bash
./test-prompts-api.sh
```

## Security Considerations

1. **Internal Secret Authentication**: All external API calls use internal secret
2. **Organization Isolation**: Prompts are scoped to specific organizations
3. **Request Tracking**: All requests have unique IDs for audit trails
4. **Input Validation**: Form data is validated on both client and server

## Future Enhancements

1. **Version History**: Display full version history for prompts
2. **Rollback Functionality**: Allow reverting to previous versions
3. **Prompt Templates**: Pre-defined prompt templates
4. **Bulk Operations**: Import/export multiple prompts
5. **Usage Analytics**: Track prompt usage and performance

## Troubleshooting

### Common Issues

1. **Build Errors**: Ensure all TypeScript types are properly defined
2. **API Connection**: Verify `ANTER_API_URL` and `INTERNAL_SECRET` configuration
3. **Authentication**: Check that user sessions are valid
4. **External API**: Ensure external API endpoints are available

### Debug Steps

1. Check browser console for client-side errors
2. Review server logs for API call failures
3. Verify environment variables are set
4. Test API endpoints directly with curl
5. Check network connectivity to external API
