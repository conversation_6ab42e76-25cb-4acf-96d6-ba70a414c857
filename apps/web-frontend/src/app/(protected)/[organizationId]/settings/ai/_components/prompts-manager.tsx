"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { useAIProviderContext } from "./ai-provider";
import { toast } from "sonner";
import { Plus, Edit, Trash2, ToggleLeft, ToggleRight, AlertCircle, Clock } from "lucide-react";

interface PromptsManagerProps {
  organizationId: string;
}

export function PromptsManager({ organizationId: _organizationId }: PromptsManagerProps) {
  const { prompts, addPrompt, updatePrompt, deletePrompt, togglePromptActive, isLoading } =
    useAIProviderContext();
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    content: "",
    isActive: true,
  });

  const handleCreate = async () => {
    try {
      await addPrompt(formData);
      setFormData({ name: "", description: "", content: "", isActive: true });
      setIsCreating(false);
      toast.success("Prompt created successfully!");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create prompt";
      toast.error(errorMessage);
    }
  };

  const handleUpdate = async (id: string) => {
    try {
      await updatePrompt(id, formData);
      setEditingId(null);
      setFormData({ name: "", description: "", content: "", isActive: true });
      toast.success("New prompt version created successfully!");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update prompt";
      toast.error(errorMessage);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deletePrompt(id);
      toast.success("Prompt deleted successfully!");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete prompt";
      toast.error(errorMessage);
    }
  };

  const handleToggleActive = async (id: string) => {
    try {
      await togglePromptActive(id);
      toast.success("Prompt status updated!");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update prompt status";
      toast.error(errorMessage);
    }
  };

  const startEdit = (prompt: {
    id: string;
    name: string;
    description: string;
    content: string;
    isActive: boolean;
  }) => {
    setEditingId(prompt.id);
    setFormData({
      name: prompt.name,
      description: prompt.description,
      content: prompt.content,
      isActive: prompt.isActive,
    });
  };

  const cancelEdit = () => {
    setEditingId(null);
    setFormData({ name: "", description: "", content: "", isActive: true });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>AI Prompts</CardTitle>
            <CardDescription>
              Manage custom prompts for AI-powered features and responses. Updates create new
              versions automatically.
            </CardDescription>
          </div>
          <Button onClick={() => setIsCreating(true)} disabled={isLoading} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Prompt
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Create/Edit Form */}
        {(isCreating || editingId) && (
          <Card className="border-dashed">
            <CardContent className="pt-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt-name">Name</Label>
                  <Input
                    id="prompt-name"
                    value={formData.name}
                    onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter prompt name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prompt-description">Description</Label>
                  <Input
                    id="prompt-description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, description: e.target.value }))
                    }
                    placeholder="Enter prompt description"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="prompt-content">Content</Label>
                <Textarea
                  id="prompt-content"
                  value={formData.content}
                  onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter prompt content..."
                  rows={6}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="prompt-active"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, isActive: checked }))
                  }
                />
                <Label htmlFor="prompt-active">Active</Label>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={editingId ? cancelEdit : () => setIsCreating(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={editingId ? () => handleUpdate(editingId) : handleCreate}
                  disabled={!formData.name || !formData.content}
                >
                  {editingId ? "Create New Version" : "Create"} Prompt
                </Button>
              </div>
              {editingId && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground bg-blue-50 p-3 rounded-md">
                  <AlertCircle className="h-4 w-4 text-blue-500" />
                  <span>
                    <strong>Version Management:</strong> When you create a new version, it
                    automatically becomes the latest version used by the AI system. Previous
                    versions are preserved for reference.
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Prompts List */}
        <div className="space-y-3">
          {prompts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No prompts created yet. Create your first prompt to get started.
            </div>
          ) : (
            prompts.map((prompt) => (
              <Card key={prompt.id} className="border">
                <CardContent className="pt-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{prompt.name}</h4>
                        <Badge variant={prompt.isActive ? "default" : "secondary"}>
                          {prompt.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{prompt.description}</p>
                      <p className="text-sm text-muted-foreground line-clamp-2">{prompt.content}</p>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-2">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>Updated: {prompt.updatedAt.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(prompt.id)}
                        disabled={isLoading}
                      >
                        {prompt.isActive ? (
                          <ToggleRight className="h-4 w-4" />
                        ) : (
                          <ToggleLeft className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => startEdit(prompt)}
                        disabled={isLoading}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(prompt.id)}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
