"use client";

import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";

// AI Settings interfaces
interface AIPrompt {
  id: string;
  name: string;
  description: string;
  content: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AIVector {
  id: string;
  name: string;
  content: string;
  embeddingCount: number;
  lastUpdated: Date;
  isActive: boolean;
}

export interface EmbeddingsData {
  organizationId: string;
  summary: {
    contentHashes: number;
    docCache: number;
    embeddings: number;
    totalKeys: number;
    totalSize: number;
    totalSizeFormatted: string;
  };
  data: {
    contentHashes: Array<{
      key: string;
      size: number;
      type: string;
      ttl: number;
    }>;
    docCache: Array<{
      key: string;
      size: number;
      type: string;
      ttl: number;
    }>;
    embeddings: Array<{
      documentId: string;
      documentName?: string;
      documentExists?: boolean;
      chunkCount: number;
      totalSize: number;
      sizeFormatted: string;
      chunks: Array<{
        key: string;
        size: number;
        type: string;
        ttl: number;
      }>;
    }>;
  };
}

interface AISettings {
  organizationId: string;
  model: string;
  temperature: number;
  maxTokens: number;
  isEnabled: boolean;
  prompts: AIPrompt[];
  vectors: AIVector[];
}

export interface LightweightEmbeddingData {
  documentId: string;
  chunkCount: number;
  totalSize: number;
  sizeFormatted: string;
}

interface AIProviderContextValue {
  organizationId: string;

  // Central refresh signal for components to react and refetch
  refreshVersion: number;
  requestRefresh: () => void;

  // AI Settings state
  settings: AISettings | null;
  isLoading: boolean;
  error: string | null;

  // Prompts management
  prompts: AIPrompt[];
  addPrompt: (prompt: Omit<AIPrompt, "id" | "createdAt" | "updatedAt">) => Promise<AIPrompt>;
  updatePrompt: (id: string, updates: Partial<AIPrompt>) => Promise<AIPrompt>;
  deletePrompt: (id: string) => Promise<void>;
  togglePromptActive: (id: string) => Promise<void>;

  // Vectors management
  vectors: AIVector[];
  addVector: (vector: Omit<AIVector, "id" | "lastUpdated">) => Promise<AIVector>;
  updateVector: (id: string, updates: Partial<AIVector>) => Promise<AIVector>;
  deleteVector: (id: string) => Promise<void>;
  toggleVectorActive: (id: string) => Promise<void>;
  generateEmbeddings: (vectorId: string) => Promise<{ success: boolean; error?: string }>;

  // Settings management
  updateSettings: (updates: Partial<AISettings>) => Promise<void>;
  testConnection: () => Promise<{ success: boolean; error?: string }>;

  // Embeddings management
  embeddingsData: EmbeddingsData | null;
  isLoadingEmbeddings: boolean;
  embeddingsError: string | null;
  fetchEmbeddings: () => Promise<void>;
  deleteEmbedding: (key: string) => Promise<{ success: boolean; error?: string }>;
  hasDocumentEmbeddings: (documentId: string) => boolean;
  getDocumentEmbeddings: (documentId: string) => EmbeddingsData["data"]["embeddings"][0] | null;

  // Lightweight document IDs cache for efficient checking
  embeddedDocumentIds: Set<string>;
  fetchEmbeddedDocumentIds: () => Promise<void>;

  // Lightweight embedding data cache for document details
  lightweightEmbeddings: Map<string, LightweightEmbeddingData>;
  getLightweightEmbedding: (documentId: string) => LightweightEmbeddingData | null;
}

const AIProviderContext = createContext<AIProviderContextValue | undefined>(undefined);

interface AIProviderProps {
  organizationId: string;
  children: React.ReactNode;
}

export function AIProvider({ organizationId, children }: AIProviderProps) {
  const [refreshVersion, setRefreshVersion] = useState(0);
  const [settings, setSettings] = useState<AISettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [prompts, setPrompts] = useState<AIPrompt[]>([]);
  const [vectors, setVectors] = useState<AIVector[]>([]);

  // Embeddings state
  const [embeddingsData, setEmbeddingsData] = useState<EmbeddingsData | null>(null);
  const [isLoadingEmbeddings, setIsLoadingEmbeddings] = useState(false);
  const [embeddingsError, setEmbeddingsError] = useState<string | null>(null);

  // Lightweight cache for document IDs that have embeddings
  const [embeddedDocumentIds, setEmbeddedDocumentIds] = useState<Set<string>>(new Set());

  // Lightweight cache for embedding data (chunk count, size, etc.)
  const [lightweightEmbeddings, setLightweightEmbeddings] = useState<
    Map<string, LightweightEmbeddingData>
  >(new Map());

  const requestRefresh = useCallback(() => {
    setRefreshVersion((v) => v + 1);
  }, []);

  // Load prompts from API
  const loadPrompts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/ai/prompts?organizationId=${organizationId}`, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to load prompts: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Failed to load prompts");
      }

      // Transform API response to match our interface
      const transformedPrompts: AIPrompt[] = (data.prompts || []).map(
        (prompt: {
          id: string;
          name: string;
          description: string;
          content: string;
          isActive: boolean;
          createdAt: string;
          updatedAt: string;
        }) => ({
          id: prompt.id,
          name: prompt.name,
          description: prompt.description,
          content: prompt.content,
          isActive: prompt.isActive,
          createdAt: new Date(prompt.createdAt),
          updatedAt: new Date(prompt.updatedAt),
        }),
      );

      setPrompts(transformedPrompts);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load prompts";
      setError(errorMessage);
      console.error("Error loading prompts:", err);
    } finally {
      setIsLoading(false);
    }
  }, [organizationId]);

  // Load initial data
  useEffect(() => {
    loadPrompts();
  }, [loadPrompts, refreshVersion]);

  // Prompts management
  const addPrompt = useCallback<AIProviderContextValue["addPrompt"]>(
    async (promptData) => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch("/api/ai/prompts", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            organizationId,
            prompt: promptData,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to create prompt: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to create prompt");
        }

        // Transform API response to match our interface
        const newPrompt: AIPrompt = {
          id: data.prompt.id,
          name: data.prompt.name,
          description: data.prompt.description,
          content: data.prompt.content,
          isActive: data.prompt.isActive,
          createdAt: new Date(data.prompt.createdAt),
          updatedAt: new Date(data.prompt.updatedAt),
        };

        setPrompts((prev) => [...prev, newPrompt]);
        requestRefresh();
        return newPrompt;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to add prompt";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [organizationId, requestRefresh],
  );

  const updatePrompt = useCallback<AIProviderContextValue["updatePrompt"]>(
    async (id, updates) => {
      try {
        setIsLoading(true);
        setError(null);

        // Find the existing prompt to get its current data
        const existingPrompt = prompts.find((p) => p.id === id);
        if (!existingPrompt) {
          throw new Error("Prompt not found");
        }

        // Create a new version with the updates (versioning system)
        const updatedPromptData = {
          name: updates.name ?? existingPrompt.name,
          description: updates.description ?? existingPrompt.description,
          content: updates.content ?? existingPrompt.content,
          isActive: updates.isActive ?? existingPrompt.isActive,
        };

        const response = await fetch("/api/ai/prompts", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            organizationId,
            prompt: updatedPromptData,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update prompt: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to update prompt");
        }

        // Transform API response to match our interface
        const newVersionPrompt: AIPrompt = {
          id: data.prompt.id,
          name: data.prompt.name,
          description: data.prompt.description,
          content: data.prompt.content,
          isActive: data.prompt.isActive,
          createdAt: new Date(data.prompt.createdAt),
          updatedAt: new Date(data.prompt.updatedAt),
        };

        // Replace the old prompt with the new version
        setPrompts((prev) => prev.map((p) => (p.id === id ? newVersionPrompt : p)));
        requestRefresh();
        return newVersionPrompt;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to update prompt";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [organizationId, prompts, requestRefresh],
  );

  const deletePrompt = useCallback<AIProviderContextValue["deletePrompt"]>(
    async (id) => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(
          `/api/ai/prompts?organizationId=${organizationId}&promptId=${id}`,
          {
            method: "DELETE",
            credentials: "include",
          },
        );

        if (!response.ok) {
          throw new Error(`Failed to delete prompt: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to delete prompt");
        }

        setPrompts((prev) => prev.filter((p) => p.id !== id));
        requestRefresh();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to delete prompt";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [organizationId, requestRefresh],
  );

  const togglePromptActive = useCallback<AIProviderContextValue["togglePromptActive"]>(
    async (id) => {
      const prompt = prompts.find((p) => p.id === id);
      if (prompt) {
        await updatePrompt(id, { isActive: !prompt.isActive });
      }
    },
    [prompts, updatePrompt],
  );

  // Vectors management
  const addVector = useCallback<AIProviderContextValue["addVector"]>(
    async (vectorData) => {
      try {
        setIsLoading(true);
        setError(null);

        // TODO: Replace with actual API call
        const newVector: AIVector = {
          ...vectorData,
          id: crypto.randomUUID(),
          lastUpdated: new Date(),
        };

        setVectors((prev) => [...prev, newVector]);
        requestRefresh();
        return newVector;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to add vector";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [requestRefresh],
  );

  const updateVector = useCallback<AIProviderContextValue["updateVector"]>(
    async (id, updates) => {
      try {
        setIsLoading(true);
        setError(null);

        // TODO: Replace with actual API call
        const updatedVector: AIVector = {
          ...vectors.find((v) => v.id === id)!,
          ...updates,
          lastUpdated: new Date(),
        };

        setVectors((prev) => prev.map((v) => (v.id === id ? updatedVector : v)));
        requestRefresh();
        return updatedVector;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to update vector";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [vectors, requestRefresh],
  );

  const deleteVector = useCallback<AIProviderContextValue["deleteVector"]>(
    async (id) => {
      try {
        setIsLoading(true);
        setError(null);

        // TODO: Replace with actual API call
        setVectors((prev) => prev.filter((v) => v.id !== id));
        requestRefresh();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to delete vector";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [requestRefresh],
  );

  const toggleVectorActive = useCallback<AIProviderContextValue["toggleVectorActive"]>(
    async (id) => {
      const vector = vectors.find((v) => v.id === id);
      if (vector) {
        await updateVector(id, { isActive: !vector.isActive });
      }
    },
    [vectors, updateVector],
  );

  const generateEmbeddings = useCallback<AIProviderContextValue["generateEmbeddings"]>(
    async (vectorId) => {
      try {
        setIsLoading(true);
        setError(null);

        // TODO: Replace with actual API call
        const vector = vectors.find((v) => v.id === vectorId);
        if (!vector) {
          throw new Error("Vector not found");
        }

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Update embedding count
        await updateVector(vectorId, {
          embeddingCount: vector.embeddingCount + 100,
          lastUpdated: new Date(),
        });

        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to generate embeddings";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setIsLoading(false);
      }
    },
    [vectors, updateVector],
  );

  // Settings management
  const updateSettings = useCallback<AIProviderContextValue["updateSettings"]>(
    async (updates) => {
      try {
        setIsLoading(true);
        setError(null);

        // TODO: Replace with actual API call
        setSettings((prev) => (prev ? { ...prev, ...updates } : null));
        requestRefresh();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to update settings";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [requestRefresh],
  );

  const testConnection = useCallback<AIProviderContextValue["testConnection"]>(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // TODO: Replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Connection test failed";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Helper function to fetch document names
  const fetchDocumentNames = useCallback(
    async (documentIds: string[]) => {
      if (documentIds.length === 0) return {};

      try {
        const response = await fetch(
          `http://localhost:5005/v1/organizations/${organizationId}/files/names?ids=${documentIds.join(",")}`,
        );

        if (!response.ok) {
          console.warn("Failed to fetch document names:", response.status);
          return {};
        }

        const result = await response.json();
        const nameMap: Record<string, { name: string; exists: boolean }> = {};

        result.documents.forEach((doc: { id: string; name: string; exists: boolean }) => {
          nameMap[doc.id] = { name: doc.name, exists: doc.exists };
        });

        return nameMap;
      } catch (error) {
        console.warn("Error fetching document names:", error);
        return {};
      }
    },
    [organizationId],
  );

  // Embeddings management
  const fetchEmbeddings = useCallback<AIProviderContextValue["fetchEmbeddings"]>(async () => {
    try {
      setIsLoadingEmbeddings(true);
      setEmbeddingsError(null);

      const response = await fetch(`/api/ai/embeddings?organizationId=${organizationId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Extract document IDs for validation
      const documentIds: string[] = [];
      result.data.embeddings.forEach((embedding: any) => {
        if (
          embedding.documentId &&
          embedding.documentId !== "unknown" &&
          embedding.documentId !== ""
        ) {
          // Remove 'doc-' prefix if present
          const cleanId = embedding.documentId.startsWith("doc-")
            ? embedding.documentId.substring(4)
            : embedding.documentId;
          documentIds.push(cleanId);
        }
      });

      // Fetch document existence validation
      const documentNames = await fetchDocumentNames(documentIds);

      // Add document names and existence status to embeddings data
      result.data.embeddings = result.data.embeddings.map((embedding: any) => {
        const cleanId = embedding.documentId.startsWith("doc-")
          ? embedding.documentId.substring(4)
          : embedding.documentId;

        const docInfo = documentNames[cleanId];
        return {
          ...embedding,
          // Use name from API response if available, otherwise fallback to fetched name
          documentName: embedding.name || docInfo?.name || "",
          documentExists: docInfo?.exists || false,
        };
      });

      setEmbeddingsData(result);

      // Update the lightweight document IDs cache
      const documentIdsSet = new Set<string>();
      result.data.embeddings.forEach((embedding: any) => {
        if (
          embedding.documentId &&
          embedding.documentId !== "unknown" &&
          embedding.documentId !== ""
        ) {
          // Remove 'doc-' prefix if present
          const cleanId = embedding.documentId.startsWith("doc-")
            ? embedding.documentId.substring(4)
            : embedding.documentId;
          documentIdsSet.add(cleanId);
        }
      });
      setEmbeddedDocumentIds(documentIdsSet);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch embeddings data";
      setEmbeddingsError(errorMessage);
      console.error("Failed to fetch embeddings data:", err);
    } finally {
      setIsLoadingEmbeddings(false);
    }
  }, [organizationId, fetchDocumentNames]);

  const deleteEmbedding = useCallback<AIProviderContextValue["deleteEmbedding"]>(
    async (key: string) => {
      try {
        setIsLoadingEmbeddings(true);
        setEmbeddingsError(null);

        // Parse the key to extract documentId
        // Key format: embeddings:org-xxx:doc-xxx::
        const keyParts = key.split(":");
        if (keyParts.length < 4 || keyParts[0] !== "embeddings") {
          throw new Error("Invalid key format");
        }

        const documentId = keyParts[2]; // Extract documentId from the key
        const cleanOrgId = organizationId.startsWith("org-")
          ? organizationId.substring(4)
          : organizationId;

        const response = await fetch(
          `/api/ai/embeddings?organizationId=${cleanOrgId}&documentId=${documentId}`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              "X-Organization-Id": organizationId,
            },
          },
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          // Refresh embeddings data after successful deletion
          await fetchEmbeddings();
          return { success: true };
        } else {
          return { success: false, error: result.error || "Failed to delete embedding" };
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to delete embedding";
        setEmbeddingsError(errorMessage);
        console.error("Failed to delete embedding:", err);
        return { success: false, error: errorMessage };
      } finally {
        setIsLoadingEmbeddings(false);
      }
    },
    [organizationId, fetchEmbeddings],
  );

  const hasDocumentEmbeddings = useCallback<AIProviderContextValue["hasDocumentEmbeddings"]>(
    (documentId: string) => {
      if (!embeddingsData) return false;
      return embeddingsData.data.embeddings.some(
        (embedding) => embedding.documentId === documentId,
      );
    },
    [embeddingsData],
  );

  const getDocumentEmbeddings = useCallback<AIProviderContextValue["getDocumentEmbeddings"]>(
    (documentId: string) => {
      if (!embeddingsData) return null;
      return (
        embeddingsData.data.embeddings.find((embedding) => embedding.documentId === documentId) ||
        null
      );
    },
    [embeddingsData],
  );

  const getLightweightEmbedding = useCallback<AIProviderContextValue["getLightweightEmbedding"]>(
    (documentId: string) => {
      return lightweightEmbeddings.get(documentId) || null;
    },
    [lightweightEmbeddings],
  );

  // Lightweight function to fetch document IDs and basic embedding data (for documents page)
  const fetchEmbeddedDocumentIds = useCallback<
    AIProviderContextValue["fetchEmbeddedDocumentIds"]
  >(async () => {
    try {
      setIsLoadingEmbeddings(true);
      setEmbeddingsError(null);

      const response = await fetch(`/api/ai/embeddings?organizationId=${organizationId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Extract document IDs and lightweight embedding data
      const documentIds = new Set<string>();
      const lightweightData = new Map<string, LightweightEmbeddingData>();

      result.data.embeddings.forEach((embedding: any) => {
        if (
          embedding.documentId &&
          embedding.documentId !== "unknown" &&
          embedding.documentId !== ""
        ) {
          // Remove 'doc-' prefix if present
          const cleanId = embedding.documentId.startsWith("doc-")
            ? embedding.documentId.substring(4)
            : embedding.documentId;

          documentIds.add(cleanId);

          // Store lightweight embedding data for document details
          lightweightData.set(cleanId, {
            documentId: cleanId,
            chunkCount: embedding.chunkCount || 0,
            totalSize: embedding.totalSize || 0,
            sizeFormatted: embedding.sizeFormatted || "0 B",
          });
        }
      });

      setEmbeddedDocumentIds(documentIds);
      setLightweightEmbeddings(lightweightData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch document IDs";
      setEmbeddingsError(errorMessage);
      console.error("Failed to fetch document IDs:", err);
    } finally {
      setIsLoadingEmbeddings(false);
    }
  }, [organizationId]);

  const value = useMemo<AIProviderContextValue>(
    () => ({
      organizationId,
      refreshVersion,
      requestRefresh,
      settings,
      isLoading,
      error,
      prompts,
      addPrompt,
      updatePrompt,
      deletePrompt,
      togglePromptActive,
      vectors,
      addVector,
      updateVector,
      deleteVector,
      toggleVectorActive,
      generateEmbeddings,
      updateSettings,
      testConnection,
      embeddingsData,
      isLoadingEmbeddings,
      embeddingsError,
      fetchEmbeddings,
      deleteEmbedding,
      hasDocumentEmbeddings,
      getDocumentEmbeddings,
      embeddedDocumentIds,
      fetchEmbeddedDocumentIds,
      lightweightEmbeddings,
      getLightweightEmbedding,
    }),
    [
      organizationId,
      refreshVersion,
      requestRefresh,
      settings,
      isLoading,
      error,
      prompts,
      addPrompt,
      updatePrompt,
      deletePrompt,
      togglePromptActive,
      vectors,
      addVector,
      updateVector,
      deleteVector,
      toggleVectorActive,
      generateEmbeddings,
      updateSettings,
      testConnection,
      embeddingsData,
      isLoadingEmbeddings,
      embeddingsError,
      fetchEmbeddings,
      deleteEmbedding,
      hasDocumentEmbeddings,
      getDocumentEmbeddings,
      embeddedDocumentIds,
      fetchEmbeddedDocumentIds,
      lightweightEmbeddings,
      getLightweightEmbedding,
    ],
  );

  return <AIProviderContext.Provider value={value}>{children}</AIProviderContext.Provider>;
}

export function useAIProviderContext(): AIProviderContextValue {
  const ctx = useContext(AIProviderContext);
  if (!ctx) {
    throw new Error("useAIProviderContext must be used within an AIProvider");
  }
  return ctx;
}
