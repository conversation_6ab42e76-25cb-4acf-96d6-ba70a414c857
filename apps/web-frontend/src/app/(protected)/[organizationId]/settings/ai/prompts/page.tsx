import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import { PageHeader } from "@/components/layout/page-header";
import { AIProvider } from "../_components/ai-provider";
import { PromptsManager } from "../_components/prompts-manager";

export const metadata: Metadata = {
  title: "Prompts",
  description: "Manage your organization's prompts for AI-powered features.",
};

interface PromptsPageProps {
  params: Promise<{ organizationId: string }>;
}

export default async function PromptsPage({ params }: PromptsPageProps) {
  const { organizationId } = await params;

  return (
    <>
      <PageHeader
        title="Prompts"
        description="Manage your organization's prompts for AI-powered features."
      />
      <AIProvider organizationId={organizationId}>
        <PromptsManager organizationId={organizationId} />
      </AIProvider>
    </>
  );
}
