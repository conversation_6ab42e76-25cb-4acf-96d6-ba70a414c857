import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import { PageHeader } from "@/components/layout/page-header";
import { AIProvider } from "../_components/ai-provider";
import { PromptsManager } from "../_components/prompts-manager";

export const metadata: Metadata = {
  title: "AI Prompts",
  description: "Manage your organization's AI prompts with automatic versioning.",
};

interface PromptsPageProps {
  params: Promise<{ organizationId: string }>;
}

export default async function PromptsPage({ params }: PromptsPageProps) {
  const { organizationId } = await params;

  return (
    <>
      <PageHeader
        title="AI Prompts"
        description="Manage your organization's AI prompts with automatic versioning."
      />
      <AIProvider organizationId={organizationId}>
        <PromptsManager organizationId={organizationId} />
      </AIProvider>
    </>
  );
}
