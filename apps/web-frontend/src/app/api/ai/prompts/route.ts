import { NextResponse, NextRequest } from "next/server";

export const dynamic = "force-dynamic";

interface PromptRequest {
  name: string;
  description: string;
  content: string;
  isActive: boolean;
}

// Helper function to get environment configuration
function getApiConfiguration():
  | { error: string }
  | { baseUrl: string; internalSecret: string; timeout: number } {
  const baseUrl = process.env.ANTER_API_URL;
  const internalSecret = process.env.INTERNAL_SECRET;
  const timeout = parseInt(process.env.ANTER_API_TIMEOUT || "30000", 10);

  if (!baseUrl || !internalSecret) {
    return { error: "Server configuration missing ANTER_API_URL or INTERNAL_SECRET" };
  }

  return { baseUrl, internalSecret, timeout };
}

// Helper function to create API headers
function createApiHeaders(
  internalSecret: string,
  organizationId: string,
  requestId: string,
  timestamp: string,
): Record<string, string> {
  return {
    "Content-Type": "application/json",
    "X-Internal-Secret": internalSecret,
    "X-Request-ID": requestId,
    "X-Organization-ID": organizationId,
    "X-Timestamp": timestamp,
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: "Missing organizationId query parameter" },
        { status: 400 },
      );
    }

    const config = getApiConfiguration();
    if ("error" in config) {
      return NextResponse.json({ success: false, error: config.error }, { status: 500 });
    }

    const { baseUrl, internalSecret, timeout } = config;

    // Generate correlation IDs for tracking
    const requestId = `prompt_list_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const timestamp = new Date().toISOString();

    // Call the external AI API
    const url = new URL("/v1/external/prompts", baseUrl);
    url.searchParams.set("organizationId", organizationId);

    const headers = createApiHeaders(internalSecret, organizationId, requestId, timestamp);

    console.log("🔍 Calling external prompts list API:", {
      url: url.toString(),
      organizationId,
      requestId,
    });

    const response = await fetch(url.toString(), {
      method: "GET",
      headers,
      signal: AbortSignal.timeout(timeout),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "");
      console.error("External prompts API error:", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });

      return NextResponse.json(
        {
          success: false,
          error: `External API error: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ""}`,
        },
        { status: response.status },
      );
    }

    const data = await response.json();

    console.log("✅ External prompts list API success:", {
      organizationId,
      requestId,
      promptsCount: data.prompts?.length || 0,
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error("Prompts list API error:", error);

    if (error instanceof Error && error.name === "TimeoutError") {
      return NextResponse.json(
        { success: false, error: "Request timeout - external API took too long to respond" },
        { status: 504 },
      );
    }

    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { organizationId, prompt } = body as {
      organizationId: string;
      prompt: PromptRequest;
    };

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: "Missing organizationId in request body" },
        { status: 400 },
      );
    }

    if (!prompt || !prompt.name || !prompt.content) {
      return NextResponse.json(
        { success: false, error: "Missing required prompt fields: name and content" },
        { status: 400 },
      );
    }

    const config = getApiConfiguration();
    if ("error" in config) {
      return NextResponse.json({ success: false, error: config.error }, { status: 500 });
    }

    const { baseUrl, internalSecret, timeout } = config;

    // Generate correlation IDs for tracking
    const requestId = `prompt_create_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const timestamp = new Date().toISOString();

    // Prepare the request for the external AI API
    const promptRequest = {
      organizationId,
      prompt: {
        name: prompt.name,
        description: prompt.description || "",
        content: prompt.content,
        isActive: prompt.isActive ?? true,
      },
    };

    // Call the external AI API
    const url = new URL("/v1/external/prompts", baseUrl);
    const headers = createApiHeaders(internalSecret, organizationId, requestId, timestamp);

    console.log("🔍 Calling external prompts create API:", {
      url: url.toString(),
      organizationId,
      promptName: prompt.name,
      requestId,
    });

    const response = await fetch(url.toString(), {
      method: "POST",
      headers,
      body: JSON.stringify(promptRequest),
      signal: AbortSignal.timeout(timeout),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "");
      console.error("External prompts create API error:", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });

      return NextResponse.json(
        {
          success: false,
          error: `External API error: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ""}`,
        },
        { status: response.status },
      );
    }

    const data = await response.json();

    console.log("✅ External prompts create API success:", {
      organizationId,
      requestId,
      promptId: data.prompt?.id,
      promptName: data.prompt?.name,
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error("Prompts create API error:", error);

    if (error instanceof Error && error.name === "TimeoutError") {
      return NextResponse.json(
        { success: false, error: "Request timeout - external API took too long to respond" },
        { status: 504 },
      );
    }

    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");
    const promptId = searchParams.get("promptId");

    if (!organizationId || !promptId) {
      return NextResponse.json(
        { success: false, error: "Missing organizationId or promptId query parameters" },
        { status: 400 },
      );
    }

    const config = getApiConfiguration();
    if ("error" in config) {
      return NextResponse.json({ success: false, error: config.error }, { status: 500 });
    }

    const { baseUrl, internalSecret, timeout } = config;

    // Generate correlation IDs for tracking
    const requestId = `prompt_delete_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const timestamp = new Date().toISOString();

    // Call the external AI API
    const url = new URL(`/v1/external/prompts/${promptId}`, baseUrl);
    const headers = createApiHeaders(internalSecret, organizationId, requestId, timestamp);

    console.log("🔍 Calling external prompts delete API:", {
      url: url.toString(),
      organizationId,
      promptId,
      requestId,
    });

    const response = await fetch(url.toString(), {
      method: "DELETE",
      headers,
      signal: AbortSignal.timeout(timeout),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "");
      console.error("External prompts delete API error:", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });

      return NextResponse.json(
        {
          success: false,
          error: `External API error: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ""}`,
        },
        { status: response.status },
      );
    }

    const data = await response.json();

    console.log("✅ External prompts delete API success:", {
      organizationId,
      requestId,
      promptId,
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error("Prompts delete API error:", error);

    if (error instanceof Error && error.name === "TimeoutError") {
      return NextResponse.json(
        { success: false, error: "Request timeout - external API took too long to respond" },
        { status: 504 },
      );
    }

    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 });
  }
}
