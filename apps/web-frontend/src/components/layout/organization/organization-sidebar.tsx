"use client";

import React from "react";
import { <PERSON>tings, CreditCard, Users, Package2, WandSparkles, Database } from "lucide-react";
import { OrganizationSidebarProps } from "@/types/organization";
import { usePathname, useRouter } from "next/navigation";
import { useOrganization } from "@/providers/organization-provider";
import { buildOrgRoute } from "@/lib/routes";
import { SidebarLayout, SidebarNav } from "@/components/layout/sidebar";
import { SidebarNavItem } from "@/types/sidebar";

const ORGANIZATION_NAV_ITEMS = [
  {
    id: "general",
    name: "General",
    description: "General organization settings",
    path: "/settings/general",
    icon: Settings,
  },
  {
    id: "members",
    name: "Members",
    description: "Team member management",
    path: "/settings/members",
    icon: Users,
  },
  {
    id: "billing",
    name: "Billing & Subscription",
    description: "Manage billing and subscription",
    path: "/settings/billing",
    icon: CreditCard,
  },
  {
    id: "products",
    name: "Products",
    description: "Manage products",
    path: "/settings/products",
    icon: Package2,
  },
  {
    id: "ai",
    name: "AI",
    description: "AI settings and configuration",
    path: "/settings/ai",
    icon: WandS<PERSON><PERSON>,
    children: [
      {
        id: "vectors",
        name: "Vectors",
        description: "Vector database management",
        path: "/settings/ai/vectors",
        icon: Database,
      },
      {
        id: "prompts",
        name: "Prompts",
        description: "Prompts management",
        path: "/settings/ai/prompts",
        icon: Database,
      },
    ],
  },
];

export const OrganizationSidebar: React.FC<OrganizationSidebarProps> = ({
  isCollapsed,
  onClose,
  isMobile = false,
  currentPath: _currentPath = "",
  onBackToDashboard,
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const { currentOrganizationId } = useOrganization();

  const orgNavItems = ORGANIZATION_NAV_ITEMS.map((item) => ({
    ...item,
    path: currentOrganizationId ? buildOrgRoute(currentOrganizationId, item.path) : item.path,
    children: item.children?.map((child) => ({
      ...child,
      path: currentOrganizationId ? buildOrgRoute(currentOrganizationId, child.path) : child.path,
    })),
  }));

  const isRouteActive = (routePath: string): boolean => {
    return pathname.includes(routePath);
  };

  const handleBackToDashboard = () => {
    onBackToDashboard();
    if (currentOrganizationId) {
      router.push(buildOrgRoute(currentOrganizationId, "/dashboard"));
    }
  };

  return (
    <SidebarLayout
      layoutType="organization"
      isCollapsed={isCollapsed}
      isMobile={isMobile}
      onClose={onClose}
      showBack
      onBack={handleBackToDashboard}
    >
      <SidebarNav
        items={orgNavItems as SidebarNavItem[]}
        isCollapsed={isCollapsed}
        currentPath={pathname}
        isRouteActive={(p?: string) => (p ? isRouteActive(p) : false)}
      />
    </SidebarLayout>
  );
};
